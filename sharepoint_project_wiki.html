
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SharePoint Sync and Local Data Processing with Python</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.2em;
        }
        .header .date {
            margin-top: 10px;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #667eea;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #667eea;
            font-size: 1.4em;
        }
        .code-section {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .code-section h4 {
            margin-top: 0;
            color: #90cdf4;
        }
        pre {
            background: #1a202c;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }
        code {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .results-table th {
            background: #667eea;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }
        .results-table td {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
        }
        .results-table tr:hover {
            background: #f7fafc;
        }
        ol, ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        .results-section h4 {
            color: #667eea;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 SharePoint Sync and Local Data Processing with Python</h1>
            <div class="date">Generated on: 2025-06-10 17:14:09</div>
        </div>
        
        
        <div class="section">
            <h3>🎯 Objective</h3>
            <p>1. Today I will process files up to 200MB and then see how it goes</p>
<p>2. Complete the notes in wiki for documentation</p>
<p>3. Connect SharePoint through app configuration</p>
<p>4. Use APIs to download documents and process large files efficiently. Mark speed for reading and writing</p>
<p>5. Sync data locally without connecting API like before, read from local OneDrive</p>

        </div>
        
        <div class="section">
            <h3>📋 Implementation Steps</h3>
            <ol>
<li><strong>Set up SharePoint connection locally</strong>
<ul>
<li>Access SharePoint site via: https://taureauai.sharepoint.com/sites/genai</li>
<li>Choose sync folder → click Sync</li>
<li>OneDrive will sync to local folder (e.g., C:\Users\<USER>\Taureau AI\genai - Test)</li>
</ul>
</li>
<li><strong>Ensure file Excel is in sync folder</strong>
<ul>
<li>Check folder contains sample files</li>
<li>File test: cars_data.xlsx (~20 rows)</li>
<li>File test: cars_data_large.xlsx (~10,000 rows)</li>
</ul>
</li>
<li><strong>Python Script for reading Excel files</strong>
<ul>
<li>Use pandas library for efficient data processing</li>
<li>Handle large files with chunking and offset reading</li>
<li>Implement error handling for robust file processing</li>
</ul>
</li>
</ol>

        </div>
        
        <div class="section">
            <h3>Python Script for Reading Excel Files</h3>
            
        <div class="code-section">
            <h4>💻 Python Script for Reading Excel Files</h4>
            
            <pre><code class="language-python">import pandas as pd

file_path = "C:\Users\<USER>\Taureau AI\genai - Test\cars_data_large.xlsx"

try:
    # First 100 lines
    df = pd.read_excel(file_path, nrows=100)
    print("First 100 Sample Data:")
    print(df)

    # Offset 5000-5100
    offset_df = pd.read_excel(file_path, skiprows=4999, nrows=100)
    print("\nData from line 5000:")
    print(offset_df)

except Exception as e:
    print(f"❌ Error: {e}")</code></pre>
        </div>
        
        </div>
        
        <div class="section">
            <h3>SharePoint API Connection</h3>
            
        <div class="code-section">
            <h4>💻 SharePoint API Connection</h4>
            
            <pre><code class="language-python">from msal import ConfidentialClientApplication
import requests

# Configuration
CONFIG = {
    "client_id": "your-client-id",
    "client_secret": "your-client-secret", 
    "tenant_id": "your-tenant-id",
    "site_hostname": "taureauai.sharepoint.com",
    "site_path": "/sites/genai"
}

def get_access_token():
    app = ConfidentialClientApplication(
        CONFIG["client_id"],
        authority=f"https://login.microsoftonline.com/{CONFIG['tenant_id']}",
        client_credential=CONFIG["client_secret"]
    )
    token_response = app.acquire_token_for_client(
        scopes=["https://graph.microsoft.com/.default"]
    )
    return token_response.get("access_token")</code></pre>
        </div>
        
        </div>
        
        <div class="section">
            <h3>📊 Test Results</h3>
            
        <div class="results-section">
            <h4>📊 Test Results</h4>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>Local File</th><th>Size (rows)</th><th>Result</th><th>Notes</th>
                    </tr>
                </thead>
                <tbody>
        <tr><td>cars_data.xlsx</td><td>N/A</td><td>❌ File not found</td><td>File does not exist locally</td></tr><tr><td>cars_data_large.xlsx</td><td>10,000</td><td>✅ ✅ Successfully read - Large file</td><td>Read 10,000 rows × 6 columns successfully</td></tr><tr><td>test video.mp4</td><td>196.8 MB</td><td>✅ ✅ Downloaded successfully</td><td>Video file ready for processing</td></tr><tr><td></td><td></td><td>✅ Available</td><td>Last modified: 2025-06-09</td></tr><tr><td></td><td></td><td>✅ Available</td><td>Last modified: 2025-06-10</td></tr>
                </tbody>
            </table>
        </div>
        
        </div>
        
        <div class="section">
            <h3>📝 Summary</h3>
            <ul>
<li>📌 Method works, no token or API required for local sync</li>
<li>📌 Fast document reading, suitable for large files</li>
<li>📌 Can handle Excel files efficiently, basic data processing training</li>
<li>✅ Successfully processed 3 local files</li>
<li>📌 SharePoint integration working</li>
</ul>

        </div>
        
    </div>
</body>
</html>
        