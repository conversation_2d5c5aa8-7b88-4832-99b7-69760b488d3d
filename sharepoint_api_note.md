📁 SharePoint API Download và Local Processing bằng Python

🎯 Mục tiêu
Sử dụng Microsoft Graph API để đồng bộ thư mục SharePoint về máy local, tải về các file Excel và Video bằng Python ( pandas ).

📋 Các bước thực hiện

1. Đồng bộ thư mục SharePoint về local
   • Truy cập vào site SharePoint (ví dụ: https://taureauai.sharepoint.com/sites/genai )
   • Chọn thư mục cần sync → nhấn Sync.
   • OneDrive sẽ tự đồng đồng bộ thư mục này về ổ đĩa máy (Ví dụ: C:\Users\<USER>\Taurus AI\genai - Test ).

2. Đảm bảo file Excel đã nằm trong thư mục được sync
   • Check biểu tượng đồng bộ màu xanh bên cạnh file.
   • File test nhỏ: cars_data.xlsx (~20 dòng).
   • File test lớn: cars_data_large.xlsx (~10.000 dòng).

🐍 Python Script để download file từ SharePoint API

import requests
import pandas as pd
from msal import ConfidentialClientApplication

# SharePoint config
CONFIG = {
    "client_id": "29be3219-b709-4950-b119-53958725bc80",
    "site_hostname": "taureauai.sharepoint.com",
    "site_path": "/sites/genai",
    "folder_path": "Test"
}

try:
    # Authentication và download
    access_token = get_access_token()
    site_id = get_site_id(access_token)
    files = list_and_analyze_files(access_token, site_id)
    
    # Download video file
    success = download_large_file_with_progress(access_token, site_id, "test video.mp4")
    print("✅ Download completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")

📊 Kết quả thử nghiệm

| Local File | Số dòng | Kết quả |
|------------|---------|---------|
| cars_data_large.xlsx | 10,000 | Đọc thành công |
| test video.mp4 | Large file | Đọc thành công, không lag |
| Download với progress tracking | ✅ | Dùng skiprows và nrows trong pandas |

❌ Tổng kết

• Phương pháp này không cần token hay API phụ hợp với quyền truy cập hiện tại.
• Tốc độ đọc nhanh, phù hợp để xử lý file lớn nội bộ.
• Có thể sử dụng với các file Excel khác: thoa đơn, báo cáo, dữ liệu training 🚀
