# 📁 SharePoint File Download & Processing System

## 🎯 Objective
Today I will process file uploads of 200MB and then see how it goes.
Complete the noted tasks in the wiki for you.

## 📋 Main Tasks

1. **Connect SharePoint through app configuration**
2. **Use APIs to download documents and process large data files. Evaluate reading and writing speed afterwards**  
3. **Sync data in offline mode without connecting to API like before, read from local OneDrive**

---

## 🚀 Implementation Steps

### 1. Setting up SharePoint connection via API
- **Access SharePoint site**: `taureauai.sharepoint.com/sites/genai`
- **Choose target folder** → `Test` folder
- **Authentication**: Using Microsoft Graph API with app credentials

### 2. Check file status in SharePoint folder
- **Check folder structure**: Review folder content via API
- **Test file**: `cars_data.xlsx` (~20 rows)
- **Test large file**: `cars_data_large.xlsx` (~10,000 rows)
- **Video file**: `test video.mp4` (large file testing)

---

## 🐍 Python Scripts Overview

### `sharepoint_simple.py` - Main SharePoint API Handler

**Key Features:**
- 🔐 **Authentication**: Microsoft Graph API with MSAL
- 📁 **File Listing**: Browse SharePoint folder contents
- 📥 **File Download**: Download files with progress tracking
- 📊 **File Analysis**: Automatic Excel and video file detection
- 🎬 **Video Support**: Special handling for video files

**Configuration:**
```python
CONFIG = {
    "client_id": "29be3219-b709-4950-b119-53958725bc80",
    "client_secret": "****************************************", 
    "tenant_id": "9cb2bebd-6862-4d5f-8a77-ee591e704d99",
    "site_hostname": "taureauai.sharepoint.com",
    "site_path": "/sites/genai",
    "folder_path": "Test"
}
```

**Main Functions:**
- `get_access_token()` - Authenticate with Microsoft Graph
- `get_site_id()` - Get SharePoint site ID
- `list_and_analyze_files()` - List and analyze all files
- `download_specific_file()` - Download single file
- `download_large_file_with_progress()` - Download with progress tracking

### `download_video.py` - Video Download Tool

**Purpose:** Specialized tool for downloading video files from SharePoint

**Key Features:**
- 🎬 **Video Focus**: Specifically designed for video file downloads
- 📊 **Progress Tracking**: Real-time download progress
- ✅ **File Verification**: Check file integrity after download
- 📁 **Local Storage**: Save to current directory

**Target File:** `test video.mp4`

---

## 🔧 How to Use

### Running the Main Script
```bash
python sharepoint_simple.py
```

**What it does:**
1. Authenticates with SharePoint
2. Lists all files in the Test folder
3. Shows file details (size, dates, type)
4. Identifies video files automatically

### Running the Video Downloader
```bash
python download_video.py
```

**What it does:**
1. Connects to SharePoint
2. Downloads `test video.mp4` specifically
3. Shows download progress
4. Verifies file integrity

---

## 📊 Test Results

| File Type | File Name | Size | Result | Notes |
|-----------|-----------|------|---------|-------|
| Excel | cars_data.xlsx | ~20 rows | ✅ Success | Fast processing |
| Excel Large | cars_data_large.xlsx | ~10,000 rows | ✅ Success | No lag detected |
| Video | test video.mp4 | Large file | ✅ Success | Progress tracking works |

---

## 🎉 Summary

- **API method works**: No token issues, can replace sync approach
- **Fast file processing**: Suitable for large file downloads
- **Progress tracking**: Real-time feedback for large files
- **File verification**: Ensures download integrity
- **Video support**: Special handling for multimedia files 🚀

---

## 🔍 Technical Details

**Dependencies:**
- `requests` - HTTP requests to Graph API
- `pandas` - Excel file analysis
- `msal` - Microsoft authentication
- `os` - File system operations

**Authentication Flow:**
1. Client credentials flow with MSAL
2. Acquire token for Graph API scope
3. Use token for all SharePoint operations

**Download Strategy:**
- Streaming downloads for large files
- Chunk-based progress tracking (8KB chunks)
- File size verification after download
- Resume capability (checks existing files)
